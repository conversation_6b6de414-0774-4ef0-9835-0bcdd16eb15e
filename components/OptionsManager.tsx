'use client';

import { useState, useEffect } from 'react';

interface OptionsManagerProps {
  fieldName: string;
  fieldLabel: string;
  value: string;
  onChange: (value: string) => void;
  isOpen: boolean;
  onClose: () => void;
  onOptionsChange?: () => void; // 新增：选项变更时的回调
}

export default function OptionsManager({
  fieldName,
  fieldLabel,
  value,
  onChange,
  isOpen,
  onClose,
  onOptionsChange
}: OptionsManagerProps) {
  const [options, setOptions] = useState<string[]>([]);
  const [newOption, setNewOption] = useState('');
  const [loading, setLoading] = useState(false);

  // 获取默认选项
  const getDefaultOptions = (fieldName: string): string[] => {
    switch (fieldName) {
      case 'overallAssessment':
        return [
          '该生表现优秀，各方面发展均衡',
          '该生学习态度端正，成绩稳步提升',
          '该生积极向上，具有良好的发展潜力',
          '该生在某些方面表现突出，需继续保持',
          '该生基础扎实，但需要在某些方面加强努力'
        ];
      case 'futureExpectations':
        return [
          '希望能在学习上更加主动积极',
          '期待在各学科均衡发展',
          '希望能发挥特长，全面发展',
          '期望能提高自主学习能力',
          '希望能在团队合作中发挥更大作用'
        ];
      case 'improvementSuggestions':
        return [
          '建议加强基础知识的巩固练习',
          '建议多参与课堂讨论，提高表达能力',
          '建议制定合理的学习计划并坚持执行',
          '建议多阅读课外书籍，拓宽知识面',
          '建议积极参与集体活动，培养团队精神'
        ];
      case 'subjectStrengths':
        return [
          '语文阅读理解能力强',
          '数学逻辑思维清晰',
          '英语口语表达流利',
          '理科思维敏捷',
          '文科知识面广',
          '艺术天赋突出',
          '体育运动能力强'
        ];
      case 'subjectWeaknesses':
        return [
          '数学计算准确性有待提高',
          '语文写作表达需要加强',
          '英语词汇量需要扩大',
          '理科实验操作需要练习',
          '文科记忆背诵需要加强',
          '注意力集中度需要提升',
          '学习方法需要改进'
        ];
      case 'classPosition':
        return [
          '无职位',
          '班长',
          '副班长',
          '学习委员',
          '体育委员',
          '文艺委员',
          '生活委员',
          '纪律委员',
          '宣传委员',
          '组织委员',
          '科代表',
          '小组长'
        ];
      default:
        return [];
    }
  };

  // 加载选项
  const loadOptions = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        // 没有 token，使用默认选项
        const defaultOptions = getDefaultOptions(fieldName);
        setOptions(defaultOptions);
        return;
      }

      const response = await fetch(`/api/custom-options?fieldName=${fieldName}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setOptions(data.options || []);
      } else {
        // 如果获取失败（包括 token 验证失败或表不存在），使用默认选项
        if (data.useDefault) {
          console.log('使用默认选项:', data.message);
        } else {
          console.log('API 获取选项失败，使用默认选项:', data.message);
        }
        const defaultOptions = getDefaultOptions(fieldName);
        setOptions(defaultOptions);
      }
    } catch (error) {
      console.error('加载选项失败:', error);
      // 如果请求失败，使用默认选项
      const defaultOptions = getDefaultOptions(fieldName);
      setOptions(defaultOptions);
    } finally {
      setLoading(false);
    }
  };

  // 添加选项
  const addOption = async () => {
    if (!newOption.trim()) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/custom-options', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          fieldName,
          optionValue: newOption.trim()
        })
      });

      const data = await response.json();

      if (data.success) {
        setNewOption('');
        await loadOptions(); // 重新加载选项列表
        onOptionsChange?.(); // 通知父组件选项已更新
      } else {
        // 如果 API 失败，暂时添加到本地状态
        if (data.useDefault || (data.message && (
          data.message.includes('relation') && data.message.includes('does not exist') ||
          data.message.includes('认证') ||
          data.message.includes('token') ||
          data.message.includes('未授权')
        ))) {
          // 表不存在或认证失败，使用本地状态
          const newOptions = [...options, newOption.trim()];
          setOptions(newOptions);
          setNewOption('');
          onOptionsChange?.(); // 通知父组件选项已更新
          if (data.useDefault) {
            alert('选项已添加（本地模式）。注意：数据库表未创建，选项不会永久保存。');
          } else {
            alert('选项已添加（本地模式）。注意：重新加载页面后此选项将丢失。如需永久保存，请重新登录。');
          }
        } else {
          alert(data.message || '添加选项失败');
        }
      }
    } catch (error) {
      console.error('添加选项失败:', error);
      // 如果网络错误，也使用本地状态
      const newOptions = [...options, newOption.trim()];
      setOptions(newOptions);
      setNewOption('');
      onOptionsChange?.(); // 通知父组件选项已更新
      alert('选项已添加（本地模式）。注意：重新加载页面后此选项将丢失。如需永久保存，请重新登录。');
    } finally {
      setLoading(false);
    }
  };

  // 删除选项
  const deleteOption = async (optionValue: string) => {
    if (!confirm(`确定要删除选项"${optionValue}"吗？`)) {
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/custom-options?fieldName=${fieldName}&optionValue=${encodeURIComponent(optionValue)}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        await loadOptions(); // 重新加载选项列表
        onOptionsChange?.(); // 通知父组件选项已更新
        // 如果删除的是当前选中的选项，清空选择
        if (value === optionValue) {
          onChange('');
        }
      } else {
        // 如果 API 失败，暂时从本地状态删除
        if (data.useDefault || (data.message && (
          data.message.includes('relation') && data.message.includes('does not exist') ||
          data.message.includes('认证') ||
          data.message.includes('token') ||
          data.message.includes('未授权')
        ))) {
          // 表不存在或认证失败，使用本地状态
          const newOptions = options.filter(opt => opt !== optionValue);
          setOptions(newOptions);
          onOptionsChange?.(); // 通知父组件选项已更新
          if (value === optionValue) {
            onChange('');
          }
          if (data.useDefault) {
            alert('选项已删除（本地模式）。注意：数据库表未创建，更改不会永久保存。');
          } else {
            alert('选项已删除（本地模式）。注意：重新加载页面后此更改将丢失。如需永久保存，请重新登录。');
          }
        } else {
          alert(data.message || '删除选项失败');
        }
      }
    } catch (error) {
      console.error('删除选项失败:', error);
      // 如果网络错误，也使用本地状态
      const newOptions = options.filter(opt => opt !== optionValue);
      setOptions(newOptions);
      onOptionsChange?.(); // 通知父组件选项已更新
      if (value === optionValue) {
        onChange('');
      }
      alert('选项已删除（本地模式）。注意：重新加载页面后此更改将丢失。如需永久保存，请重新登录。');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      loadOptions();
    }
  }, [isOpen, fieldName]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">管理选项 - {fieldLabel}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 添加新选项 */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            添加新选项
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              value={newOption}
              onChange={(e) => setNewOption(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入新选项内容"
              onKeyPress={(e) => e.key === 'Enter' && addOption()}
            />
            <button
              onClick={addOption}
              disabled={loading || !newOption.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? '添加中...' : '添加'}
            </button>
          </div>
        </div>

        {/* 选项列表 */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            现有选项（点击选择）
          </label>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {options.map((option, index) => (
              <div
                key={index}
                className={`flex items-center justify-between p-2 border rounded-md cursor-pointer hover:bg-gray-50 ${
                  value === option ? 'bg-blue-50 border-blue-300' : 'border-gray-200'
                }`}
                onClick={() => onChange(option)}
              >
                <span className="flex-1 text-sm">{option}</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteOption(option);
                  }}
                  className="text-red-500 hover:text-red-700 ml-2"
                  title="删除选项"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            ))}
            {options.length === 0 && (
              <div className="text-center text-gray-500 py-4">
                暂无选项，请添加新选项
              </div>
            )}
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
}
